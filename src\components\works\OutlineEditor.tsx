/**
 * 大纲编辑器组件
 */
import React, { useState, useEffect } from 'react';
import { Outline, OutlineFormData } from '@/types/outline';

interface OutlineEditorProps {
  outline: Outline;
  workId: number;
  onSave: (outline: Outline) => void;
  isSaving: boolean;
  lastSavedAt: Date | null;
}

/**
 * 大纲编辑器组件
 */
export const OutlineEditor: React.FC<OutlineEditorProps> = ({
  outline,
  workId,
  onSave,
  isSaving,
  lastSavedAt
}) => {
  // 表单数据状态
  const [formData, setFormData] = useState<OutlineFormData>({
    title: outline.title,
    content: outline.content
  });

  // 是否有未保存的更改
  const [hasChanges, setHasChanges] = useState(false);

  // 监听outline变化，更新表单数据
  useEffect(() => {
    setFormData({
      title: outline.title,
      content: outline.content
    });
    setHasChanges(false);
  }, [outline]);

  // 处理表单数据变化
  const handleChange = (field: keyof OutlineFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    setHasChanges(true);
  };

  // 保存大纲
  const handleSave = () => {
    if (!hasChanges) return;

    const updatedOutline: Outline = {
      ...outline,
      title: formData.title.trim() || '未命名大纲',
      content: formData.content,
      updatedAt: new Date()
    };

    // 保存到localStorage
    try {
      const storageKey = `work_outlines_${workId}`;
      const stored = localStorage.getItem(storageKey);
      if (stored) {
        const outlines = JSON.parse(stored);
        const updatedOutlines = outlines.map((item: Outline) =>
          item.id === outline.id ? updatedOutline : item
        );
        localStorage.setItem(storageKey, JSON.stringify(updatedOutlines));
      }
    } catch (error) {
      console.error('保存大纲失败:', error);
    }

    if (onSave) {
      onSave(updatedOutline);
    }
    setHasChanges(false);
  };

  // 键盘快捷键保存
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.ctrlKey && e.key === 's') {
      e.preventDefault();
      handleSave();
    }
  };

  return (
    <div className="h-full flex flex-col bg-white" onKeyDown={handleKeyDown}>
      {/* 顶部工具栏 */}
      <div className="flex-shrink-0 px-6 py-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <span className="material-icons text-[#4a90e2]">list_alt</span>
              <h2 className="text-lg font-semibold text-gray-800">大纲编辑</h2>
            </div>
            {hasChanges && (
              <span className="text-sm text-orange-600 bg-orange-100 px-2 py-1 rounded">
                未保存
              </span>
            )}
          </div>
          
          <div className="flex items-center space-x-3">
            {lastSavedAt && (
              <span className="text-sm text-gray-500">
                最后保存: {lastSavedAt.toLocaleTimeString()}
              </span>
            )}
            <button
              onClick={handleSave}
              disabled={!hasChanges || isSaving}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                hasChanges && !isSaving
                  ? 'bg-[#4a90e2] text-white hover:bg-[#357abd]'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }`}
            >
              {isSaving ? '保存中...' : '保存'}
            </button>
          </div>
        </div>
      </div>

      {/* 编辑区域 */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* 标题输入 */}
        <div className="flex-shrink-0 px-6 py-4 border-b border-gray-200">
          <input
            type="text"
            value={formData.title}
            onChange={(e) => handleChange('title', e.target.value)}
            placeholder="请输入大纲标题..."
            className="w-full text-xl font-semibold text-gray-800 bg-transparent border-none outline-none placeholder-gray-400"
          />
        </div>

        {/* 内容编辑区 */}
        <div className="flex-1 px-6 py-4 overflow-hidden">
          <textarea
            value={formData.content}
            onChange={(e) => handleChange('content', e.target.value)}
            placeholder="请输入大纲内容..."
            className="w-full h-full resize-none border-none outline-none text-gray-700 leading-relaxed placeholder-gray-400 bg-transparent"
            style={{ fontSize: '16px', lineHeight: '1.6' }}
          />
        </div>
      </div>

      {/* 底部提示 */}
      <div className="flex-shrink-0 px-6 py-2 border-t border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between text-sm text-gray-500">
          <span>Ctrl+S 快速保存</span>
          <span>字数: {formData.content.length}</span>
        </div>
      </div>
    </div>
  );
};
