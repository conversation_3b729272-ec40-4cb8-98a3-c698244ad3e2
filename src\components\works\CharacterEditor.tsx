/**
 * 角色编辑器组件
 */
import React, { useState, useEffect } from 'react';
import { Character, CharacterFormData, CharacterGender } from '@/types/character';

interface CharacterEditorProps {
  character: Character;
  workId: number;
  onSave?: (character: Character) => void;
  isSaving?: boolean;
  lastSavedAt?: Date | null;
}

export const CharacterEditor: React.FC<CharacterEditorProps> = ({
  character,
  workId,
  onSave,
  isSaving = false,
  lastSavedAt
}) => {
  const [formData, setFormData] = useState<CharacterFormData>({
    name: character.name,
    gender: character.gender,
    personality: character.personality,
    background: character.background
  });

  const [hasChanges, setHasChanges] = useState(false);

  // 当角色变化时更新表单数据
  useEffect(() => {
    setFormData({
      name: character.name,
      gender: character.gender,
      personality: character.personality,
      background: character.background
    });
    setHasChanges(false);
  }, [character]);

  // 处理表单输入变化
  const handleInputChange = (field: keyof CharacterFormData, value: string | CharacterGender) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    setHasChanges(true);
  };

  // 保存角色
  const handleSave = () => {
    if (!hasChanges) return;

    const updatedCharacter: Character = {
      ...character,
      name: formData.name.trim() || '未命名角色',
      gender: formData.gender,
      personality: formData.personality,
      background: formData.background,
      updatedAt: new Date()
    };

    // 保存到localStorage
    try {
      const storageKey = `character_cards_${workId}`;
      const stored = localStorage.getItem(storageKey);
      if (stored) {
        const characters = JSON.parse(stored);
        const updatedCharacters = characters.map((char: Character) =>
          char.id === character.id ? updatedCharacter : char
        );
        localStorage.setItem(storageKey, JSON.stringify(updatedCharacters));
      }
    } catch (error) {
      console.error('保存角色失败:', error);
    }

    if (onSave) {
      onSave(updatedCharacter);
    }
    setHasChanges(false);
  };

  // 自动保存功能
  useEffect(() => {
    if (hasChanges) {
      const timer = setTimeout(() => {
        handleSave();
      }, 2000); // 2秒后自动保存

      return () => clearTimeout(timer);
    }
  }, [formData, hasChanges]);

  return (
    <div className="h-full flex flex-col bg-white">
      {/* 顶部工具栏 */}
      <div className="flex-shrink-0 px-6 py-4 border-b border-[rgba(156,111,224,0.2)] bg-gradient-to-r from-[rgba(156,111,224,0.05)] to-[rgba(156,111,224,0.02)]">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="w-8 h-8 rounded-full bg-gradient-to-br from-[#9c6fe0] to-[#8c5fd0] flex items-center justify-center mr-3 text-white shadow-md">
              <span className="material-icons text-lg">person</span>
            </div>
            <h2 className="text-lg font-medium text-text-dark">角色编辑</h2>
          </div>
          <div className="flex items-center space-x-3">
            {hasChanges && (
              <button
                onClick={handleSave}
                disabled={isSaving}
                className="px-4 py-2 bg-gradient-to-br from-[#9c6fe0] to-[#8c5fd0] text-white text-sm rounded-lg hover:shadow-md transition-all disabled:opacity-50"
              >
                {isSaving ? '保存中...' : '保存'}
              </button>
            )}
            {lastSavedAt && (
              <span className="text-xs text-gray-500">
                最后保存: {lastSavedAt.toLocaleTimeString()}
              </span>
            )}
          </div>
        </div>
      </div>

      {/* 编辑区域 */}
      <div className="flex-1 px-6 py-4 overflow-y-auto">
        <div className="w-full max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* 左侧：编辑表单 */}
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-text-dark mb-4">编辑角色信息</h3>

              {/* 基本信息 */}
              <div className="space-y-4">
                {/* 角色名称 */}
                <div>
                  <label className="block text-text-dark font-medium text-base mb-2">角色名称：</label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className="w-full px-3 py-2 border border-[rgba(156,111,224,0.3)] rounded-lg focus:outline-none focus:border-[#9c6fe0] transition-all text-base"
                    placeholder="输入角色名称"
                  />
                </div>

                {/* 角色性别 */}
                <div>
                  <label className="block text-text-dark font-medium text-base mb-2">角色性别：</label>
                  <select
                    value={formData.gender}
                    onChange={(e) => handleInputChange('gender', e.target.value as CharacterGender)}
                    className="w-full px-3 py-2 border border-[rgba(156,111,224,0.3)] rounded-lg focus:outline-none focus:border-[#9c6fe0] transition-all text-base"
                  >
                    <option value="男">男</option>
                    <option value="女">女</option>
                    <option value="无">无</option>
                  </select>
                </div>

                {/* 角色性格 */}
                <div>
                  <label className="block text-text-dark font-medium text-base mb-2">角色性格：</label>
                  <textarea
                    value={formData.personality}
                    onChange={(e) => handleInputChange('personality', e.target.value)}
                    className="w-full px-3 py-2 border border-[rgba(156,111,224,0.3)] rounded-lg focus:outline-none focus:border-[#9c6fe0] transition-all h-24 resize-none text-base"
                    placeholder="描述角色的性格特点..."
                  />
                </div>

                {/* 背景信息 */}
                <div>
                  <label className="block text-text-dark font-medium text-base mb-2">背景信息：</label>
                  <textarea
                    value={formData.background}
                    onChange={(e) => handleInputChange('background', e.target.value)}
                    className="w-full px-3 py-2 border border-[rgba(156,111,224,0.3)] rounded-lg focus:outline-none focus:border-[#9c6fe0] transition-all h-32 resize-none text-base"
                    placeholder="描述角色的背景故事..."
                  />
                </div>
              </div>
            </div>

            {/* 右侧：预览区域 */}
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-text-dark mb-4">角色预览</h3>

              <div className="bg-gray-50 rounded-lg p-4 border border-[rgba(156,111,224,0.2)]">
                <div className="space-y-3 text-base leading-relaxed">
                  <div>
                    <span className="font-medium text-text-dark">角色名称：</span>
                    <span className="text-text-dark">{formData.name || '未命名角色'}</span>
                  </div>

                  <div>
                    <span className="font-medium text-text-dark">角色性别：</span>
                    <span className="text-text-dark">{formData.gender}</span>
                  </div>

                  <div>
                    <span className="font-medium text-text-dark">角色性格：</span>
                    <span className="text-text-dark">{formData.personality || '暂无描述'}</span>
                  </div>

                  <div>
                    <span className="font-medium text-text-dark">背景信息：</span>
                    <span className="text-text-dark">{formData.background || '暂无描述'}</span>
                  </div>
                </div>
              </div>

              {/* 提示信息 */}
              <div className="text-sm text-gray-600 bg-blue-50 rounded-lg p-3 border border-blue-200">
                <div className="flex items-start">
                  <span className="material-icons text-blue-500 text-sm mr-2 mt-0.5">info</span>
                  <div>
                    <p className="font-medium text-blue-700 mb-1">编辑提示</p>
                    <p>右侧预览区域显示角色信息的最终格式，修改左侧表单内容会实时更新预览。</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
