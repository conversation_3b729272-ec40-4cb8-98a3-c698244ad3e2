/**
 * 设定编辑器组件
 */
import React, { useState, useEffect } from 'react';
import { Setting, SettingFormData, SETTING_CATEGORIES, SETTING_CATEGORY_LABELS } from '@/types/setting';

interface SettingEditorProps {
  setting: Setting;
  workId: number;
  onSave: (setting: Setting) => void;
  isSaving: boolean;
  lastSavedAt: Date | null;
}

/**
 * 设定编辑器组件
 */
export const SettingEditor: React.FC<SettingEditorProps> = ({
  setting,
  workId,
  onSave,
  isSaving,
  lastSavedAt
}) => {
  // 表单数据状态
  const [formData, setFormData] = useState<SettingFormData>({
    title: setting.title,
    content: setting.content,
    category: setting.category
  });

  // 是否有未保存的更改
  const [hasChanges, setHasChanges] = useState(false);

  // 监听setting变化，更新表单数据
  useEffect(() => {
    setFormData({
      title: setting.title,
      content: setting.content,
      category: setting.category
    });
    setHasChanges(false);
  }, [setting]);

  // 处理表单数据变化
  const handleChange = (field: keyof SettingFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    setHasChanges(true);
  };

  // 保存设定
  const handleSave = () => {
    if (!hasChanges) return;

    const updatedSetting: Setting = {
      ...setting,
      title: formData.title.trim() || '未命名设定',
      content: formData.content,
      category: formData.category,
      updatedAt: new Date()
    };

    // 保存到localStorage
    try {
      const storageKey = `work_settings_${workId}`;
      const stored = localStorage.getItem(storageKey);
      if (stored) {
        const settings = JSON.parse(stored);
        const updatedSettings = settings.map((item: Setting) =>
          item.id === setting.id ? updatedSetting : item
        );
        localStorage.setItem(storageKey, JSON.stringify(updatedSettings));
      }
    } catch (error) {
      console.error('保存设定失败:', error);
    }

    if (onSave) {
      onSave(updatedSetting);
    }
    setHasChanges(false);
  };

  // 键盘快捷键保存
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.ctrlKey && e.key === 's') {
      e.preventDefault();
      handleSave();
    }
  };

  return (
    <div className="h-full flex flex-col bg-white" onKeyDown={handleKeyDown}>
      {/* 顶部工具栏 */}
      <div className="flex-shrink-0 px-6 py-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <span className="material-icons text-[#f39c12]">settings</span>
              <h2 className="text-lg font-semibold text-gray-800">设定编辑</h2>
            </div>
            {hasChanges && (
              <span className="text-sm text-orange-600 bg-orange-100 px-2 py-1 rounded">
                未保存
              </span>
            )}
          </div>
          
          <div className="flex items-center space-x-3">
            {lastSavedAt && (
              <span className="text-sm text-gray-500">
                最后保存: {lastSavedAt.toLocaleTimeString()}
              </span>
            )}
            <button
              onClick={handleSave}
              disabled={!hasChanges || isSaving}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                hasChanges && !isSaving
                  ? 'bg-[#f39c12] text-white hover:bg-[#d68910]'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }`}
            >
              {isSaving ? '保存中...' : '保存'}
            </button>
          </div>
        </div>
      </div>

      {/* 编辑区域 */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* 标题和分类输入 */}
        <div className="flex-shrink-0 px-6 py-4 border-b border-gray-200 space-y-4">
          <input
            type="text"
            value={formData.title}
            onChange={(e) => handleChange('title', e.target.value)}
            placeholder="请输入设定标题..."
            className="w-full text-xl font-semibold text-gray-800 bg-transparent border-none outline-none placeholder-gray-400"
          />
          
          <div className="flex items-center space-x-4">
            <label className="text-sm font-medium text-gray-600">分类:</label>
            <select
              value={formData.category}
              onChange={(e) => handleChange('category', e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#f39c12] focus:border-transparent"
            >
              {Object.entries(SETTING_CATEGORY_LABELS).map(([key, label]) => (
                <option key={key} value={key}>
                  {label}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* 内容编辑区 */}
        <div className="flex-1 px-6 py-4 overflow-hidden">
          <textarea
            value={formData.content}
            onChange={(e) => handleChange('content', e.target.value)}
            placeholder="请输入设定内容..."
            className="w-full h-full resize-none border-none outline-none text-gray-700 leading-relaxed placeholder-gray-400 bg-transparent"
            style={{ fontSize: '16px', lineHeight: '1.6' }}
          />
        </div>
      </div>

      {/* 底部提示 */}
      <div className="flex-shrink-0 px-6 py-2 border-t border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between text-sm text-gray-500">
          <span>Ctrl+S 快速保存</span>
          <div className="flex items-center space-x-4">
            <span>分类: {SETTING_CATEGORY_LABELS[formData.category as keyof typeof SETTING_CATEGORY_LABELS]}</span>
            <span>字数: {formData.content.length}</span>
          </div>
        </div>
      </div>
    </div>
  );
};
